import { NextRequest, NextResponse } from 'next/server';
import { query, handleDatabaseError } from '@/lib/database/connection';
import { API_ENDPOINTS } from '@/lib/api-endpoints';
import { RowDataPacket } from 'mysql2';

// 分类数据类型
interface Category {
  id: number;
  name: string;
  slug: string;
  description: string;
  icon: string;
  color: string;
  sort_order: number;
  is_active: boolean;
}

// 统一响应格式
function createResponse(success: boolean, data?: any, message?: string, code?: string, status: number = 200) {
  return NextResponse.json({
    success,
    data,
    message,
    code,
  }, { status });
}

// 错误响应
function createErrorResponse(message: string, code?: string, status: number = 400) {
  return NextResponse.json({
    success: false,
    message,
    code,
  }, { status });
}

export async function GET(request: NextRequest) {
  try {
    // 获取热门分类
    const categories = await query<(Category & RowDataPacket)[]>(`
      SELECT 
        id,
        name,
        slug,
        description,
        icon,
        color,
        sort_order,
        is_active
      FROM service_categories 
      WHERE is_active = true 
      ORDER BY sort_order ASC, id ASC
    `);

    // 格式化响应数据
    const formattedCategories = categories.map(category => ({
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      icon: category.icon,
      color: category.color,
      sortOrder: category.sort_order,
      isActive: category.is_active,
    }));

    return createResponse(true, {
      categories: formattedCategories,
      total: formattedCategories.length,
    }, '获取分类成功');

  } catch (error) {
    console.error('Get categories error:', error);

    // 处理数据库错误
    if (error instanceof Error) {
      const dbError = handleDatabaseError(error);
      
      if (dbError.message.includes('数据库连接失败')) {
        return createErrorResponse(
          '数据库连接失败，请稍后重试',
          'DATABASE_CONNECTION_ERROR',
          503
        );
      }
    }

    // 其他错误
    return createErrorResponse(
      '获取分类失败，请稍后重试',
      'INTERNAL_SERVER_ERROR',
      500
    );
  }
}

// 处理不支持的HTTP方法
export async function POST() {
  return createErrorResponse(
    '不支持的请求方法',
    'METHOD_NOT_ALLOWED',
    405
  );
}

export async function PUT() {
  return createErrorResponse(
    '不支持的请求方法',
    'METHOD_NOT_ALLOWED',
    405
  );
}

export async function DELETE() {
  return createErrorResponse(
    '不支持的请求方法',
    'METHOD_NOT_ALLOWED',
    405
  );
}
