import { z } from 'zod';

// 用户名验证规则
export const usernameSchema = z.string()
  .min(3, '用户名至少需要3个字符')
  .max(20, '用户名不能超过20个字符')
  .regex(/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线');

// 密码验证规则
export const passwordSchema = z.string()
  .min(6, '密码至少需要6个字符')
  .max(50, '密码不能超过50个字符');

// 邮箱验证规则
export const emailSchema = z.string()
  .email('请输入有效的邮箱地址')
  .max(100, '邮箱地址不能超过100个字符');

// 登录请求验证
export const loginSchema = z.object({
  username: usernameSchema,
  password: passwordSchema,
});

// 注册请求验证
export const registerSchema = z.object({
  username: usernameSchema,
  password: passwordSchema,
  confirmPassword: z.string().optional(),
  email: emailSchema.optional(),
}).refine((data) => {
  if (data.confirmPassword && data.password !== data.confirmPassword) {
    return false;
  }
  return true;
}, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword'],
});

// 修改密码验证
export const changePasswordSchema = z.object({
  currentPassword: passwordSchema,
  newPassword: passwordSchema,
  confirmPassword: passwordSchema,
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: '两次输入的新密码不一致',
  path: ['confirmPassword'],
}).refine((data) => data.currentPassword !== data.newPassword, {
  message: '新密码不能与当前密码相同',
  path: ['newPassword'],
});

// 重置密码请求验证
export const resetPasswordSchema = z.object({
  email: emailSchema,
});

// 确认重置密码验证
export const confirmResetPasswordSchema = z.object({
  token: z.string().min(1, '重置令牌不能为空'),
  newPassword: passwordSchema,
  confirmPassword: passwordSchema,
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword'],
});

// 更新用户资料验证
export const updateProfileSchema = z.object({
  username: usernameSchema.optional(),
  email: emailSchema.optional(),
  bio: z.string().max(500, '个人简介不能超过500个字符').optional(),
  avatar: z.string().url('头像必须是有效的URL').optional(),
});

// 验证结果类型
export type LoginInput = z.infer<typeof loginSchema>;
export type RegisterInput = z.infer<typeof registerSchema>;
export type ChangePasswordInput = z.infer<typeof changePasswordSchema>;
export type ResetPasswordInput = z.infer<typeof resetPasswordSchema>;
export type ConfirmResetPasswordInput = z.infer<typeof confirmResetPasswordSchema>;
export type UpdateProfileInput = z.infer<typeof updateProfileSchema>;

// 验证错误处理函数
export function formatValidationErrors(error: z.ZodError): Record<string, string[]> {
  const errors: Record<string, string[]> = {};
  
  error.errors.forEach((err) => {
    const field = err.path[0] as string;
    if (!errors[field]) {
      errors[field] = [];
    }
    errors[field].push(err.message);
  });
  
  return errors;
}

// 安全验证函数
export function validateAndSanitize<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T } | { success: false; errors: Record<string, string[]> } {
  const result = schema.safeParse(data);
  
  if (result.success) {
    return { success: true, data: result.data };
  } else {
    return { success: false, errors: formatValidationErrors(result.error) };
  }
}
