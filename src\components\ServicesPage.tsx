import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import {
  Search,
  Filter,
  Grid,
  List,
  Star,
  Heart,
  ShoppingCart,
  Sparkles,
  TrendingUp,
  Award,
  Zap,
  ChevronDown,
  SlidersHorizontal,
  User,
  Bot,
  X,
  Home,
  MapPin
} from "lucide-react";

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  image: string;
  rating: number;
  reviews: number;
  category: string;
  tags: string[];
  discount?: number;
  isBestseller?: boolean;
  isNew?: boolean;
  creator: {
    name: string;
    avatar: string;
    verified?: boolean;
    location?: string;
  };
  favoriteCount: number;
  specialBadge?: "upro" | "top" | "star";
}

const mockProducts: Product[] = [
  {
    id: "1",
    name: "✨绝美品牌视觉设计，让你的LOGO成为爆款记忆点！",
    description: "姐妹们！这个设计师真的太会了😍 从LOGO到整套VI，各种风格都能拿捏住，原创度100%，商用无忧虑～绝对是品牌升级的必备神器！",
    price: 999,
    originalPrice: 1299,
    image: "https://images.unsplash.com/photo-1558655146-d09347e92766?w=400&h=400&fit=crop",
    rating: 5.0,
    reviews: 1234,
    category: "设计",
    tags: ["热门", "原创"],
    discount: 23,
    isBestseller: true,
    isNew: false,
    creator: {
      name: "设计师小王",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "北京"
    },
    favoriteCount: 892,
    specialBadge: "upro"
  },
  {
    id: "2",
    name: "🚀全栈网站开发YYDS！React+Node.js技术栈，让你的网站飞起来",
    description: "宝贝们看过来！这位程序员小哥哥技术真的绝了🔥 响应式设计+全栈开发+一键部署，从前端到后端全搞定，网站速度嗖嗖的，用户体验直接拉满！",
    price: 4999,
    image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=400&fit=crop",
    rating: 4.9,
    reviews: 856,
    category: "技术",
    tags: ["专业", "全栈"],
    isBestseller: true,
    creator: {
      name: "程序员老李",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "上海"
    },
    favoriteCount: 634,
    specialBadge: "top"
  },
  {
    id: "3",
    name: "📱小程序开发专家！微信生态全覆盖，让你的生意更上一层楼",
    description: "小程序界的扛把子！🏆 从商城到工具类，各种类型都能搞定，用户体验丝滑到飞起，后台管理系统也是一应俱全！",
    price: 2999,
    image: "https://images.unsplash.com/photo-1551650975-87deedd944c3?w=400&h=400&fit=crop",
    rating: 4.8,
    reviews: 445,
    category: "技术",
    tags: ["小程序", "微信"],
    creator: {
      name: "小程序达人",
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "深圳"
    },
    favoriteCount: 378,
    specialBadge: "star"
  },
  {
    id: "4",
    name: "🎨UI设计大神在线！让你的界面颜值爆表，用户爱不释手",
    description: "设计界的颜值担当！✨ 从APP到网页，每一个像素都精雕细琢，交互设计更是让人眼前一亮，保证让你的产品脱颖而出！",
    price: 1999,
    image: "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=400&fit=crop",
    rating: 4.9,
    reviews: 667,
    category: "设计",
    tags: ["UI设计", "交互"],
    creator: {
      name: "UI设计师小美",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616c45e1f94?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "杭州"
    },
    favoriteCount: 523,
    specialBadge: "upro"
  }
];

const filterTags = [
  { id: "bestseller", label: "热销榜", icon: TrendingUp },
  { id: "new", label: "新品", icon: Sparkles },
  { id: "discount", label: "特惠", icon: Zap },
  { id: "professional", label: "专业级", icon: Award }
];

interface ServicesPageProps {
  activeCategory?: string;
  activeSubCategory?: string | null;
  onCategoryChange?: (category: string) => void;
  onSubCategoryChange?: (subCategory: string | null) => void;
  onViewProductDetail?: (productId: string) => void;
}

export function ServicesPageContent({
  activeCategory = "popular",
  activeSubCategory,
  onCategoryChange,
  onSubCategoryChange,
  onViewProductDetail
}: ServicesPageProps) {
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState("default");
  const [favorites, setFavorites] = useState<string[]>([]);

  // 筛选状态
  const [filters, setFilters] = useState({
    timeRange: "all",
    priceRange: "all",
    rating: "all",
    serviceType: "all"
  });

  // 快速筛选状态
  const [quickFilters, setQuickFilters] = useState({
    uproServices: false,
    onlineOnly: false
  });

  // 筛选产品
  const filteredProducts = mockProducts.filter(product => {
    let matchesCategory = true;

    // 根据分类筛选
    switch (activeCategory) {
      case "popular":
        matchesCategory = !!product.isBestseller || !!product.isNew;
        break;
      case "design":
        matchesCategory = product.category === "设计" || product.category === "3D";
        break;
      case "programming":
        matchesCategory = product.category === "技术";
        break;
      case "marketing":
        matchesCategory = product.category === "营销";
        break;
      case "video":
        matchesCategory = product.category === "视频";
        break;
      case "writing":
        matchesCategory = product.category === "写作";
        break;
      case "music":
        matchesCategory = product.category === "音频";
        break;
      case "business":
        matchesCategory = product.tags.includes("专业") || product.tags.includes("数据");
        break;
      default:
        matchesCategory = true;
    }

    let matchesTags = true;
    if (selectedTags.length > 0) {
      matchesTags = selectedTags.every(tag => {
        switch (tag) {
          case "bestseller":
            return !!product.isBestseller;
          case "new":
            return !!product.isNew;
          case "discount":
            return !!product.discount;
          case "professional":
            return product.tags.includes("专业");
          default:
            return true;
        }
      });
    }

    // 快速筛选逻辑
    let matchesQuickFilters = true;

    if (quickFilters.uproServices) {
      matchesQuickFilters = matchesQuickFilters && product.specialBadge === "upro";
    }

    if (quickFilters.onlineOnly) {
      matchesQuickFilters = matchesQuickFilters && !!product.creator.verified;
    }

    return matchesCategory && matchesTags && matchesQuickFilters;
  });

  // 排序产品
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case "price-low":
        return a.price - b.price;
      case "price-high":
        return b.price - a.price;
      case "rating":
        return b.rating - a.rating;
      case "reviews":
        return b.reviews - a.reviews;
      default:
        return 0;
    }
  });

  const toggleTag = (tagId: string) => {
    setSelectedTags(prev =>
      prev.includes(tagId)
        ? prev.filter(t => t !== tagId)
        : [...prev, tagId]
    );
  };

  const toggleFavorite = (productId: string) => {
    setFavorites(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  // 获取分类名称
  const getCategoryName = (category: string) => {
    switch (category) {
      case "popular": return "热门项目";
      case "design": return "图像与设计";
      case "programming": return "编程与技术";
      case "marketing": return "数字营销";
      case "video": return "视频动画摄影";
      case "writing": return "写作&翻译";
      case "music": return "音乐与音频";
      case "business": return "商业";
      case "finance": return "财务";
      case "legal": return "法律";
      case "academic": return "学业";
      default: return "服务";
    }
  };

  // 获取分类描述
  const getCategoryDescription = (category: string) => {
    switch (category) {
      case "popular": return "发现最受欢迎的服务项目";
      case "design": return "创意设计和视觉表达服务";
      case "programming": return "专业技术开发解决方案";
      case "marketing": return "数字化营销推广服务";
      case "video": return "专业影像制作服务";
      case "writing": return "文字创作和多语言服务";
      case "music": return "音频制作和音乐创作";
      case "business": return "商业咨询和管理服务";
      case "finance": return "财务分析和投资顾问";
      case "legal": return "法律咨询和文书服务";
      case "academic": return "学术辅导和教育服务";
      default: return "发现专业服务";
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 py-6">
      {/* 面包屑导航 */}
      <div className="mb-4">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  onCategoryChange?.("popular");
                  onSubCategoryChange?.(null);
                }}
                className="flex items-center gap-1 text-slate-500 hover:text-slate-700 transition-colors cursor-pointer"
              >
                <Home className="h-4 w-4" />
                首页
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            {activeSubCategory ? (
              <>
                <BreadcrumbItem>
                  <BreadcrumbLink
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      onSubCategoryChange?.(null);
                    }}
                    className="text-slate-500 hover:text-slate-700 transition-colors cursor-pointer"
                  >
                    {getCategoryName(activeCategory)}
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage className="text-slate-900">
                    {activeSubCategory}
                  </BreadcrumbPage>
                </BreadcrumbItem>
              </>
            ) : (
              <BreadcrumbItem>
                <BreadcrumbPage className="text-slate-900">
                  {getCategoryName(activeCategory)}
                </BreadcrumbPage>
              </BreadcrumbItem>
            )}
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      {/* 页面标题 */}
      <div className="mb-6 flex items-start justify-between">
        <div>
          <h1 className="text-slate-900 mb-1">
            {activeSubCategory || getCategoryName(activeCategory)}
          </h1>
          <p className="text-slate-600">
            {activeSubCategory
              ? `${getCategoryName(activeCategory)} - ${activeSubCategory} 相关服务`
              : getCategoryDescription(activeCategory)
            }
          </p>
        </div>

        {/* 右侧咨询按钮 - 只在热门项目显示 */}
        {activeCategory === "popular" && (
          <div className="flex flex-col gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50/60 px-3 py-2 h-auto justify-start text-sm font-normal"
            >
              <User className="h-4 w-4 mr-2" />
              是否需要定制化选购？
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-purple-600 hover:text-purple-700 hover:bg-purple-50/60 px-3 py-2 h-auto justify-start text-sm font-normal"
            >
              <Bot className="h-4 w-4 mr-2" />
              可以问问AI我先需要准备哪些
            </Button>
          </div>
        )}
      </div>

      {/* 顶部筛选区域 */}
      <div className="bg-white/70 backdrop-blur-xl border border-slate-200/60 rounded-2xl p-6 mb-6">
        {/* 主要筛选区域 */}
        <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between mb-6">
          {/* 控制区域 */}
          <div className="flex items-center gap-3 w-full justify-end">
            {/* 排序方式 */}
            <div className="relative">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="appearance-none bg-slate-50/80 border border-slate-200/60 rounded-lg px-3 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500/20"
              >
                <option value="default">排序方式</option>
                <option value="price-low">价格从低到高</option>
                <option value="price-high">价格从高到低</option>
                <option value="rating">评分最高</option>
                <option value="reviews">评价最多</option>
              </select>
              <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400 pointer-events-none" />
            </div>

            {/* 视图切换 */}
            <div className="flex items-center bg-slate-100/80 rounded-lg p-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setViewMode("grid")}
                className={`p-1 h-8 w-8 ${viewMode === "grid" ? 'bg-white shadow-sm' : ''}`}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setViewMode("list")}
                className={`p-1 h-8 w-8 ${viewMode === "list" ? 'bg-white shadow-sm' : ''}`}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* 特性筛选标签 */}
        <div className="flex flex-wrap gap-2">
          {filterTags.map(tag => {
            const Icon = tag.icon;
            const isSelected = selectedTags.includes(tag.id);
            return (
              <Button
                key={tag.id}
                variant="ghost"
                size="sm"
                onClick={() => toggleTag(tag.id)}
                className={`rounded-full px-3 py-2 flex items-center gap-2 transition-all duration-200 ${
                  isSelected
                    ? 'bg-gradient-to-r from-blue-50/80 to-indigo-50/80 text-blue-700 border-blue-200/60 shadow-sm'
                    : 'bg-slate-50/80 text-slate-600 hover:bg-slate-100/80 border-slate-200/60'
                } border`}
              >
                <Icon className="h-3 w-3" />
                {tag.label}
              </Button>
            );
          })}
        </div>
      </div>

      {/* 结果统计 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <span className="text-slate-500 text-sm">
            共 {sortedProducts.length} 个结果
          </span>
        </div>
        <div className="flex items-center gap-4">
          {/* 快速筛选切换按钮 */}
          <div className="flex items-center gap-3">
            {/* Upro服务切换 */}
            <div className="flex items-center gap-2">
              <button
                onClick={() => setQuickFilters(prev => ({ ...prev, uproServices: !prev.uproServices }))}
                className={`relative inline-flex items-center w-11 h-6 rounded-full transition-colors duration-200 ${
                  quickFilters.uproServices
                    ? 'bg-blue-600'
                    : 'bg-slate-300'
                }`}
              >
                <span
                  className={`inline-block w-4 h-4 bg-white rounded-full transition-transform duration-200 ${
                    quickFilters.uproServices ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
              <span className="text-sm text-slate-600">
                专业服务
              </span>
            </div>

            {/* 在线状态切换 */}
            <div className="flex items-center gap-2">
              <button
                onClick={() => setQuickFilters(prev => ({ ...prev, onlineOnly: !prev.onlineOnly }))}
                className={`relative inline-flex items-center w-11 h-6 rounded-full transition-colors duration-200 ${
                  quickFilters.onlineOnly
                    ? 'bg-blue-600'
                    : 'bg-slate-300'
                }`}
              >
                <span
                  className={`inline-block w-4 h-4 bg-white rounded-full transition-transform duration-200 ${
                    quickFilters.onlineOnly ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
              <span className="text-sm text-slate-600">
                即时响应
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 产品网格 - 统一使用4列布局 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {sortedProducts.map((product) => {
          const isFavorited = favorites.includes(product.id);
          return (
            <Card
              key={product.id}
              className="group bg-white/90 backdrop-blur-sm border-slate-200/60 hover:shadow-xl hover:shadow-slate-200/40 transition-all duration-300 cursor-pointer overflow-hidden relative"
            >
              {/* 星级评分 - 右上角 */}
              <div className="absolute top-3 right-3 z-10 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full flex items-center gap-1">
                <Star className="h-3 w-3 text-yellow-500 fill-current" />
                <span className="text-xs font-medium text-slate-700">{product.rating}</span>
              </div>

              {/* 商品图片 */}
              <div className="relative aspect-[4/3] bg-gradient-to-br from-slate-50 to-slate-100/50 overflow-hidden">
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />

                {/* 商品标签 - 图片左下角 */}
                {product.specialBadge && (
                  <div className="absolute bottom-2 left-2 z-10">
                    {product.specialBadge === "upro" && (
                      <Badge className="bg-purple-600 text-white border-0 text-xs px-2 py-0.5">
                        U PRO
                      </Badge>
                    )}
                    {product.specialBadge === "top" && (
                      <Badge className="bg-amber-600 text-white border-0 text-xs px-2 py-0.5">
                        好评Top
                      </Badge>
                    )}
                    {product.specialBadge === "star" && (
                      <Badge className="bg-blue-600 text-white border-0 text-xs px-2 py-0.5">
                        技能之星
                      </Badge>
                    )}
                  </div>
                )}
              </div>

              <CardContent className="px-4 pt-2 pb-4">
                {/* 创作者信息和评价 */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <div className="relative">
                      <img
                        src={product.creator.avatar}
                        alt={product.creator.name}
                        className="w-6 h-6 rounded-full object-cover border border-slate-200"
                      />
                      {product.creator.verified && (
                        <div className="absolute -top-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                      )}
                    </div>
                    <span className="text-sm text-slate-600 truncate">{product.creator.name}</span>
                    {product.creator.verified && (
                      <span className="text-xs text-blue-600 bg-blue-50 px-1.5 py-0.5 rounded">认证</span>
                    )}
                  </div>
                  <div className="flex items-center gap-1 flex-shrink-0">
                    <span className="text-xs text-slate-500">
                      ({product.reviews} 评价)
                    </span>
                  </div>
                </div>

                {/* 商品名称 */}
                <h3 className="text-slate-900 mb-1 group-hover:text-blue-700 transition-colors duration-200 line-clamp-1">
                  {product.name}
                </h3>

                {/* 商品描述 */}
                <p className="text-slate-600 text-sm mb-4 line-clamp-2 min-h-[2.5rem]">
                  {product.description}
                </p>

                {/* 底部区域 */}
                <div className="flex items-end justify-between">
                  {/* 左下角 - 收藏按钮和数量 */}
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleFavorite(product.id);
                      }}
                      className={`h-8 w-8 p-0 rounded-full transition-all duration-200 ${
                        isFavorited
                          ? 'bg-red-50/90 text-red-500 hover:bg-red-100/90'
                          : 'bg-slate-50/80 text-slate-400 hover:bg-slate-100/90 hover:text-red-500'
                      }`}
                    >
                      <Heart className={`h-4 w-4 ${isFavorited ? 'fill-current' : ''}`} />
                    </Button>
                    <span className="text-xs text-slate-500">
                      {product.favoriteCount}
                    </span>
                  </div>

                  {/* 右下角 - 价格 */}
                  <div className="text-right">
                    <div className="flex items-center gap-1 justify-end">
                      <span className="text-slate-700 font-medium">RMB</span>
                      <span className="text-slate-900 font-medium">
                        {product.price}
                      </span>
                      <span className="text-slate-500 font-medium">起</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* 空状态 */}
      {sortedProducts.length === 0 && (
        <div className="text-center py-12">
          <div className="relative inline-block mb-4">
            <div className="absolute inset-0 bg-slate-200/50 rounded-full blur-xl"></div>
            <Search className="relative h-12 w-12 text-slate-400 mx-auto" />
          </div>
          <h3 className="text-slate-900 mb-2">没有找到相关服务</h3>
          <p className="text-slate-600">请尝试调整搜索条件或筛选选项</p>
        </div>
      )}
    </div>
  );
}
