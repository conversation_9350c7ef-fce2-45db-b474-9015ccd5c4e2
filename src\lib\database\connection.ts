import mysql from 'mysql2/promise';

// 数据库连接配置
export const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '123456',
  database: process.env.DB_NAME || 'ubund_db',
  port: parseInt(process.env.DB_PORT || '3306'),
  charset: 'utf8mb4',
  timezone: '+08:00',
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
};

// 数据库连接池
let pool: mysql.Pool | null = null;

// 获取数据库连接池
export function getPool(): mysql.Pool {
  if (!pool) {
    pool = mysql.createPool({
      ...dbConfig,
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0,
    });
  }
  return pool;
}

// 获取单个数据库连接
export async function getConnection(): Promise<mysql.Connection> {
  return await mysql.createConnection(dbConfig);
}

// 执行查询（使用连接池）
export async function query<T = any>(
  sql: string,
  params?: any[]
): Promise<T> {
  const pool = getPool();
  const [rows] = await pool.execute(sql, params);
  return rows as T;
}

// 执行事务
export async function transaction<T>(
  callback: (connection: mysql.Connection) => Promise<T>
): Promise<T> {
  const connection = await getConnection();
  
  try {
    await connection.beginTransaction();
    const result = await callback(connection);
    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    await connection.end();
  }
}

// 关闭连接池
export async function closePool(): Promise<void> {
  if (pool) {
    await pool.end();
    pool = null;
  }
}

// 数据库健康检查
export async function healthCheck(): Promise<boolean> {
  try {
    const connection = await getConnection();
    await connection.ping();
    await connection.end();
    return true;
  } catch (error) {
    console.error('Database health check failed:', error);
    return false;
  }
}

// 数据库错误类型
export class DatabaseError extends Error {
  public code?: string;
  public errno?: number;
  public sqlState?: string;
  public sqlMessage?: string;

  constructor(message: string, originalError?: any) {
    super(message);
    this.name = 'DatabaseError';
    
    if (originalError) {
      this.code = originalError.code;
      this.errno = originalError.errno;
      this.sqlState = originalError.sqlState;
      this.sqlMessage = originalError.sqlMessage;
    }
  }
}

// 处理数据库错误
export function handleDatabaseError(error: any): DatabaseError {
  // MySQL错误码处理
  switch (error.code) {
    case 'ER_DUP_ENTRY':
      return new DatabaseError('数据已存在，请勿重复操作', error);
    case 'ER_NO_REFERENCED_ROW_2':
      return new DatabaseError('关联数据不存在', error);
    case 'ER_ROW_IS_REFERENCED_2':
      return new DatabaseError('数据正在被使用，无法删除', error);
    case 'ER_DATA_TOO_LONG':
      return new DatabaseError('数据长度超出限制', error);
    case 'ER_BAD_NULL_ERROR':
      return new DatabaseError('必填字段不能为空', error);
    case 'ECONNREFUSED':
      return new DatabaseError('数据库连接失败', error);
    case 'ETIMEDOUT':
      return new DatabaseError('数据库连接超时', error);
    default:
      return new DatabaseError('数据库操作失败', error);
  }
}

// 导出默认连接函数
export default getConnection;
