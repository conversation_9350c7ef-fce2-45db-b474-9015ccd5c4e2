import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { 
  Search, 
  Play,
  Palette,
  Code,
  Video,
  Megaphone,
  FileText,
  Music,
  TrendingUp,
  Star
} from "lucide-react";

interface LandingPageProps {
  onNavigateToServices?: () => void;
}

export function LandingPage({ onNavigateToServices }: LandingPageProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // 热门商品数据
  const featuredProducts = [
    {
      id: "1",
      name: "品牌视觉设计",
      price: 999,
      image: "https://images.unsplash.com/photo-1558655146-d09347e92766?w=300&h=200&fit=crop",
      rating: 5.0,
      reviews: 1234
    },
    {
      id: "2", 
      name: "网站开发定制",
      price: 4999,
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=300&h=200&fit=crop",
      rating: 4.9,
      reviews: 856
    },
    {
      id: "3",
      name: "短视频制作", 
      price: 1999,
      image: "https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?w=300&h=200&fit=crop",
      rating: 4.8,
      reviews: 2341
    }
  ];

  // 分类数据
  const categories = [
    {
      id: "design",
      name: "设计与创意",
      icon: Palette,
      color: "from-blue-500 to-purple-500",
      bgColor: "bg-blue-50",
      description: "Logo设计、品牌视觉、UI/UX设计"
    },
    {
      id: "tech",
      name: "技术开发",
      icon: Code,
      color: "from-green-500 to-blue-500",
      bgColor: "bg-green-50",
      description: "网站开发、移动应用、系统集成"
    },
    {
      id: "video",
      name: "视频制作",
      icon: Video,
      color: "from-red-500 to-orange-500",
      bgColor: "bg-red-50",
      description: "短视频、宣传片、动画制作"
    },
    {
      id: "marketing",
      name: "营销推广",
      icon: Megaphone,
      color: "from-purple-500 to-pink-500",
      bgColor: "bg-purple-50",
      description: "社媒运营、广告投放、品牌策划"
    },
    {
      id: "writing",
      name: "文案写作",
      icon: FileText,
      color: "from-yellow-500 to-orange-500",
      bgColor: "bg-yellow-50",
      description: "文案撰写、翻译服务、内容创作"
    },
    {
      id: "music",
      name: "音乐与音频",
      icon: Music,
      color: "from-slate-500 to-slate-600",
      bgColor: "bg-slate-50",
      description: "配音服务、音频制作、音效设计"
    }
  ];

  const handleSearch = () => {
    // 跳转到服务页面并传递搜索词
    onNavigateToServices?.();
  };

  const handleCategoryClick = (categoryId: string) => {
    // 跳转到对应分类的服务页面
    onNavigateToServices?.();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50/30">
      {/* Hero区域 - 视频介绍 */}
      <section className="relative overflow-hidden py-28">
        {/* 视频背景 */}
        {isClient && (
          <video
            key="hero-video"
            autoPlay
            muted
            loop
            playsInline
            className="absolute inset-0 w-full h-full object-cover z-0"
            suppressHydrationWarning
          >
            <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4" />
            <source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        )}
        
        {/* 服务端渲染时的占位符 */}
        {!isClient && (
          <div className="absolute inset-0 w-full h-full bg-gradient-to-br from-blue-600/20 to-purple-600/20 z-0" />
        )}

        {/* 半透明覆盖层 */}
        
        <div className="relative z-20 max-w-7xl mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* 左侧文字内容 */}
            <div>
              <h1 className="text-slate-900 mb-6 leading-tight">
                发现专业服务
                <br />
                <span className="text-blue-600">成就无限可能</span>
              </h1>
              <p className="text-slate-600 mb-8 text-lg leading-relaxed">
                U-bund连接全球优秀创作者，为您提供设计、技术、营销等
                <br />
                各领域的专业服务。从创意到实现，我们助您实现每一个想法。
              </p>
              
              {/* 搜索框 */}
              <div className="relative mb-6">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
                <input
                  type="text"
                  placeholder="搜索您需要的服务..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="w-full pl-12 pr-4 py-4 text-slate-700 placeholder-slate-400 focus:outline-none bg-white rounded-full shadow-lg border border-slate-200/60"
                />
              </div>

              {/* 热门商品快捷按钮 */}
              <div className="flex flex-wrap gap-3">
                {featuredProducts.map((product) => (
                  <Button
                    key={product.id}
                    variant="outline"
                    size="sm"
                    onClick={onNavigateToServices}
                    className="bg-white/80 border-slate-200/60 text-slate-600 hover:text-slate-900 hover:bg-white"
                  >
                    {product.name}
                  </Button>
                ))}
              </div>
            </div>

            {/* 右侧视频/图片区域 */}
            <div className="relative">

            </div>
          </div>
        </div>
      </section>

      {/* 热门分类 */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-slate-900 mb-4">热门分类</h2>
            <p className="text-slate-600 text-lg">探索各个专业领域，找到最适合您的服务</p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories.map((category) => {
              const Icon = category.icon;
              return (
                <Card 
                  key={category.id}
                  onClick={() => handleCategoryClick(category.id)}
                  className="group bg-white/90 backdrop-blur-sm border-slate-200/60 hover:shadow-xl hover:shadow-slate-200/40 transition-all duration-300 cursor-pointer overflow-hidden"
                >
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className={`relative p-3 ${category.bgColor} rounded-xl`}>
                        <div className={`absolute inset-0 bg-gradient-to-r ${category.color} opacity-20 rounded-xl`}></div>
                        <Icon className="h-6 w-6 text-slate-700 relative z-10" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-slate-900 mb-2 group-hover:text-blue-600 transition-colors">
                          {category.name}
                        </h3>
                        <p className="text-slate-600 text-sm leading-relaxed">
                          {category.description}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* 网站介绍 */}
      <section className="py-20 bg-gradient-to-r from-slate-100/50 to-slate-200/50">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* 左侧内容 */}
            <div>
              <h2 className="text-slate-900 mb-6 leading-tight">
                为什么选择
                <span className="text-blue-600"> U-bund</span>？
              </h2>
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <Star className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-slate-900 mb-2">优质服务保障</h3>
                    <p className="text-slate-600">严格筛选认证创作者，确保每一个项目都达到专业标准</p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                    <TrendingUp className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-slate-900 mb-2">快速高效</h3>
                    <p className="text-slate-600">智能匹配系统，快速找到最适合的服务提供者</p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                    <Palette className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="text-slate-900 mb-2">创意无限</h3>
                    <p className="text-slate-600">汇聚全球创意人才，为您的项目注入无限创意灵感</p>
                  </div>
                </div>
              </div>

              <div className="mt-8">
                <Button
                  onClick={onNavigateToServices}
                  size="lg"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3"
                >
                  开始探索服务
                </Button>
              </div>
            </div>

            {/* 右侧统计信息 */}
            <div className="relative">
              <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-slate-200/60">
                <div className="grid grid-cols-2 gap-8">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600 mb-2">10,000+</div>
                    <div className="text-slate-600">优质服务</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600 mb-2">50,000+</div>
                    <div className="text-slate-600">满意客户</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-purple-600 mb-2">5,000+</div>
                    <div className="text-slate-600">认证创作者</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-orange-600 mb-2">99%</div>
                    <div className="text-slate-600">完成率</div>
                  </div>
                </div>
              </div>

              {/* 装饰元素 */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl"></div>
              <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-r from-green-400/20 to-blue-400/20 rounded-full blur-xl"></div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
