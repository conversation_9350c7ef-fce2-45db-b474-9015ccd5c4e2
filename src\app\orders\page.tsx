"use client";

import { useState, useMemo } from "react";
import { NavigationV78 } from "@/components/NavigationV78";
import { OrderCard } from "@/components/OrderCard";
import { OrderStatusTabs } from "@/components/OrderStatusTabs";
import { OrderFilters } from "@/components/OrderFilters";
import { ChatWindow } from "@/components/ChatWindow";
import { ChatFloatingButton } from "@/components/ChatFloatingButton";
import { mockOrders } from "@/data/mockOrders";
import { getStatusCounts, filterOrders } from "@/utils/orderUtils";
import { useRouter } from "next/navigation";
import { Package } from "lucide-react";

export default function Orders() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [dateFilter, setDateFilter] = useState("all");
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [notificationCount] = useState(3);

  const handleNavigate = (page: string) => {
    switch (page) {
      case "home":
      case "landing":
        router.push("/");
        break;
      case "services":
        router.push("/services");
        break;
      case "orders":
        router.push("/orders");
        break;
      case "favorites":
        router.push("/favorites");
        break;
      case "support":
        router.push("/support");
        break;
      case "gallery":
        router.push("/gallery");
        break;
      case "login":
        router.push("/login");
        break;
      case "register":
        router.push("/register");
        break;
      default:
        break;
    }
  };

  const handleOpenChat = () => {
    setIsChatOpen(true);
  };

  const handleCloseChat = () => {
    setIsChatOpen(false);
  };

  const handleMinimizeChat = () => {
    setIsChatOpen(false);
  };

  const handleViewOrderDetail = (orderId: string) => {
    router.push(`/order/${orderId}`);
  };

  // 筛选订单
  const filteredOrders = useMemo(() => {
    return filterOrders(mockOrders, searchTerm, statusFilter, dateFilter);
  }, [searchTerm, statusFilter, dateFilter]);

  const statusCounts = getStatusCounts(mockOrders);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50/30">
      <NavigationV78 
        currentPage="orders" 
        onNavigate={handleNavigate}
        onOpenChat={handleOpenChat}
      />
      
      {/* 订单页面头部 */}
      <div className="bg-white/70 backdrop-blur-xl border-b border-slate-200/60">
        <div className="max-w-7xl mx-auto px-4 py-6">
          {/* 页面标题 */}
          <div className="flex items-center gap-3 mb-8">
            <div className="relative">
              <div className="absolute inset-0 bg-blue-500/10 rounded-xl blur-sm"></div>
              <div className="relative p-3 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-xl border border-blue-200/30">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-slate-900">我的订单</h1>
              <p className="text-slate-600">管理您的所有订单</p>
            </div>
          </div>
          
          {/* 状态标签 */}
          <div className="mb-8">
            <OrderStatusTabs
              activeStatus={statusFilter}
              onStatusChange={setStatusFilter}
              statusCounts={statusCounts}
            />
          </div>

          {/* 筛选器 */}
          <OrderFilters
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            statusFilter={statusFilter}
            onStatusFilterChange={setStatusFilter}
            dateFilter={dateFilter}
            onDateFilterChange={setDateFilter}
          />
        </div>
      </div>

      {/* 订单列表 */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        {filteredOrders.length > 0 ? (
          <div className="space-y-4">
            {filteredOrders.map((order) => (
              <OrderCard 
                key={order.id} 
                order={order} 
                onViewDetail={() => handleViewOrderDetail(order.id)}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="relative inline-block mb-4">
              <div className="absolute inset-0 bg-slate-200/50 rounded-full blur-xl"></div>
              <Package className="relative h-12 w-12 text-slate-400 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-slate-900 mb-2">暂无订单</h3>
            <p className="text-slate-600">
              {searchTerm || statusFilter !== "all" || dateFilter !== "all" 
                ? "没有找到符合条件的订单，请尝试调整筛选条件"
                : "您还没有任何订单，快去购物吧！"
              }
            </p>
          </div>
        )}
      </div>

      {/* 聊天窗口 */}
      <ChatWindow isOpen={isChatOpen} onClose={handleCloseChat} onMinimize={handleMinimizeChat} />
      
      {/* 聊天悬浮球 */}
      <ChatFloatingButton 
        onClick={handleOpenChat}
        notificationCount={notificationCount}
        isVisible={!isChatOpen}
      />
    </div>
  );
}
