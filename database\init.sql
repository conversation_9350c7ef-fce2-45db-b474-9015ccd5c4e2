-- 创建数据库
CREATE DATABASE IF NOT EXISTS ubund_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE ubund_db;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  email VARCHAR(100) NULL UNIQUE,
  avatar VARCHAR(500) NULL,
  bio TEXT NULL,
  status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
  email_verified_at TIMESTAMP NULL,
  last_login_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_username (username),
  INDEX idx_email (email),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建用户会话表（用于管理refresh token）
CREATE TABLE IF NOT EXISTS user_sessions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  refresh_token VARCHAR(500) NOT NULL,
  device_info VARCHAR(500) NULL,
  ip_address VARCHAR(45) NULL,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_id (user_id),
  INDEX idx_refresh_token (refresh_token),
  INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建服务分类表
CREATE TABLE IF NOT EXISTS service_categories (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  slug VARCHAR(100) NOT NULL UNIQUE,
  description TEXT NULL,
  icon VARCHAR(100) NULL,
  color VARCHAR(50) NULL,
  sort_order INT DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_slug (slug),
  INDEX idx_sort_order (sort_order),
  INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建服务表
CREATE TABLE IF NOT EXISTS services (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  category_id INT NOT NULL,
  title VARCHAR(200) NOT NULL,
  description TEXT NOT NULL,
  price DECIMAL(10, 2) NOT NULL,
  original_price DECIMAL(10, 2) NULL,
  delivery_time INT NOT NULL DEFAULT 7, -- 交付时间（天）
  images JSON NULL, -- 存储图片URL数组
  tags JSON NULL, -- 存储标签数组
  features JSON NULL, -- 存储服务特性数组
  requirements TEXT NULL, -- 服务要求
  status ENUM('draft', 'active', 'paused', 'deleted') DEFAULT 'draft',
  is_featured BOOLEAN DEFAULT FALSE,
  is_bestseller BOOLEAN DEFAULT FALSE,
  is_new BOOLEAN DEFAULT FALSE,
  views_count INT DEFAULT 0,
  orders_count INT DEFAULT 0,
  rating DECIMAL(3, 2) DEFAULT 0.00,
  reviews_count INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (category_id) REFERENCES service_categories(id) ON DELETE RESTRICT,
  INDEX idx_user_id (user_id),
  INDEX idx_category_id (category_id),
  INDEX idx_status (status),
  INDEX idx_is_featured (is_featured),
  INDEX idx_is_bestseller (is_bestseller),
  INDEX idx_price (price),
  INDEX idx_rating (rating),
  INDEX idx_created_at (created_at),
  FULLTEXT idx_title_description (title, description)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建订单表
CREATE TABLE IF NOT EXISTS orders (
  id INT AUTO_INCREMENT PRIMARY KEY,
  order_number VARCHAR(50) NOT NULL UNIQUE,
  buyer_id INT NOT NULL,
  seller_id INT NOT NULL,
  service_id INT NOT NULL,
  title VARCHAR(200) NOT NULL,
  description TEXT NULL,
  price DECIMAL(10, 2) NOT NULL,
  status ENUM('pending', 'in_progress', 'completed', 'cancelled', 'refunded') DEFAULT 'pending',
  delivery_time INT NOT NULL,
  requirements TEXT NULL,
  delivery_files JSON NULL,
  notes TEXT NULL,
  paid_at TIMESTAMP NULL,
  started_at TIMESTAMP NULL,
  completed_at TIMESTAMP NULL,
  cancelled_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (buyer_id) REFERENCES users(id) ON DELETE RESTRICT,
  FOREIGN KEY (seller_id) REFERENCES users(id) ON DELETE RESTRICT,
  FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE RESTRICT,
  INDEX idx_order_number (order_number),
  INDEX idx_buyer_id (buyer_id),
  INDEX idx_seller_id (seller_id),
  INDEX idx_service_id (service_id),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建收藏表
CREATE TABLE IF NOT EXISTS favorites (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  service_id INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
  UNIQUE KEY unique_user_service (user_id, service_id),
  INDEX idx_user_id (user_id),
  INDEX idx_service_id (service_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建评价表
CREATE TABLE IF NOT EXISTS reviews (
  id INT AUTO_INCREMENT PRIMARY KEY,
  order_id INT NOT NULL,
  reviewer_id INT NOT NULL,
  service_id INT NOT NULL,
  seller_id INT NOT NULL,
  rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
  comment TEXT NULL,
  images JSON NULL,
  is_anonymous BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
  FOREIGN KEY (reviewer_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
  FOREIGN KEY (seller_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_order_id (order_id),
  INDEX idx_reviewer_id (reviewer_id),
  INDEX idx_service_id (service_id),
  INDEX idx_seller_id (seller_id),
  INDEX idx_rating (rating),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认服务分类
INSERT INTO service_categories (name, slug, description, icon, color, sort_order) VALUES
('设计与创意', 'design', 'Logo设计、品牌视觉、UI/UX设计', 'Palette', 'from-blue-500 to-purple-500', 1),
('技术开发', 'tech', '网站开发、移动应用、系统集成', 'Code', 'from-green-500 to-blue-500', 2),
('视频制作', 'video', '短视频、宣传片、动画制作', 'Video', 'from-red-500 to-orange-500', 3),
('营销推广', 'marketing', '社媒运营、广告投放、品牌策划', 'Megaphone', 'from-purple-500 to-pink-500', 4),
('文案写作', 'writing', '文案撰写、翻译服务、内容创作', 'FileText', 'from-yellow-500 to-orange-500', 5),
('音乐与音频', 'music', '配音服务、音频制作、音效设计', 'Music', 'from-slate-500 to-slate-600', 6);

-- 创建测试用户（密码为：123456）
INSERT INTO users (username, password, email, created_at) VALUES
('testuser', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL.hl.vHm', '<EMAIL>', NOW()),
('admin', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL.hl.vHm', '<EMAIL>', NOW());

-- 创建一些示例服务
INSERT INTO services (user_id, category_id, title, description, price, original_price, delivery_time, tags, is_featured, is_bestseller, rating, reviews_count, created_at) VALUES
(1, 1, '专业Logo设计', '为您的品牌打造独特的Logo设计，包含多个方案选择，提供源文件和使用指南', 999.00, 1299.00, 5, '["专业", "原创", "商用授权"]', TRUE, TRUE, 4.9, 156, NOW()),
(1, 2, '响应式网站开发', '使用最新技术栈开发现代化响应式网站，包含后台管理系统', 4999.00, NULL, 14, '["React", "Next.js", "全栈"]', TRUE, FALSE, 4.8, 89, NOW()),
(2, 3, '企业宣传片制作', '专业团队制作高质量企业宣传片，包含脚本策划、拍摄、后期制作', 8999.00, 12999.00, 21, '["专业", "原创", "4K"]', FALSE, TRUE, 5.0, 67, NOW());

COMMIT;
