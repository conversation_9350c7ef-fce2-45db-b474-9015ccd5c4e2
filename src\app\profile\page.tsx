"use client";

import { useState } from "react";
import { NavigationV78 } from "@/components/NavigationV78";
import { PlaceholderPage } from "@/components/PlaceholderPage";
import { ChatWindow } from "@/components/ChatWindow";
import { ChatFloatingButton } from "@/components/ChatFloatingButton";
import { useRouter } from "next/navigation";

export default function ProfilePage() {
  const router = useRouter();
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [notificationCount] = useState(3);

  const handleNavigate = (page: string) => {
    switch (page) {
      case "home":
      case "landing":
        router.push("/");
        break;
      case "services":
        router.push("/services");
        break;
      case "orders":
        router.push("/orders");
        break;
      case "favorites":
        router.push("/favorites");
        break;
      case "support":
        router.push("/support");
        break;
      case "gallery":
        router.push("/gallery");
        break;
      case "profile":
        router.push("/profile");
        break;
      case "login":
        router.push("/login");
        break;
      case "register":
        router.push("/register");
        break;
      default:
        break;
    }
  };

  const handleOpenChat = () => {
    setIsChatOpen(true);
  };

  const handleCloseChat = () => {
    setIsChatOpen(false);
  };

  const handleMinimizeChat = () => {
    setIsChatOpen(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50/30">
      <NavigationV78 
        currentPage="profile" 
        onNavigate={handleNavigate}
        onOpenChat={handleOpenChat}
      />
      
      <PlaceholderPage
        title="个人设置"
        description="用户个人资料和设置页面正在设计中，敬请期待..."
        iconColor="text-blue-600"
        gradientColors="from-blue-500/10 to-indigo-500/10"
        borderColor="border-blue-200/30"
      />

      {/* 聊天窗口 */}
      <ChatWindow isOpen={isChatOpen} onClose={handleCloseChat} onMinimize={handleMinimizeChat} />
      
      {/* 聊天悬浮球 */}
      <ChatFloatingButton 
        onClick={handleOpenChat}
        notificationCount={notificationCount}
        isVisible={!isChatOpen}
      />
    </div>
  );
}
