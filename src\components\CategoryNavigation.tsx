import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { 
  TrendingUp,
  Palette, 
  Code, 
  Megaphone, 
  Camera, 
  PenTool,
  Music,
  Briefcase,
  DollarSign,
  Scale,
  GraduationCap
} from "lucide-react";

interface CategoryNavigationProps {
  activeCategory?: string;
  onCategoryChange?: (category: string) => void;
  onSubCategoryClick?: (category: string, subCategory: string) => void;
}

export function CategoryNavigation({ activeCategory = "popular", onCategoryChange, onSubCategoryClick }: CategoryNavigationProps) {
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);
  const [isHovering, setIsHovering] = useState(false);
  const [activeSubCategory, setActiveSubCategory] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const mainCategories = [
    { id: "popular", label: "热门", icon: TrendingUp },
    { id: "design", label: "设计", icon: Palette },
    { id: "tech", label: "技术", icon: Code },
    { id: "marketing", label: "营销", icon: Megaphone },
    { id: "video", label: "视频", icon: Camera },
    { id: "writing", label: "文案", icon: PenTool },
    { id: "music", label: "音乐", icon: Music },
    { id: "business", label: "商务", icon: Briefcase },
    { id: "finance", label: "金融", icon: DollarSign },
    { id: "legal", label: "法律", icon: Scale },
    { id: "academic", label: "学业", icon: GraduationCap }
  ];

  // 子分类数据
  const subCategories = {
    popular: {
      title: "热门服务",
      sections: [
        {
          title: "设计类",
          items: ["Logo设计", "海报设计", "包装设计", "UI设计"]
        },
        {
          title: "技术类", 
          items: ["网站开发", "小程序开发", "APP开发", "系统开发"]
        }
      ]
    },
    design: {
      title: "设计",
      sections: [
        {
          title: "平面设计",
          items: ["Logo设计", "海报设计", "名片设计", "宣传册设计"]
        },
        {
          title: "UI/UX设计",
          items: ["网页设计", "APP界面", "小程序界面", "交互设计"]
        }
      ]
    },
    tech: {
      title: "技术",
      sections: [
        {
          title: "网站开发",
          items: ["企业官网", "电商网站", "响应式网站", "定制开发"]
        },
        {
          title: "移动开发",
          items: ["微信小程序", "APP开发", "H5开发", "跨平台开发"]
        }
      ]
    }
  };

  const handleCategoryClick = (categoryId: string) => {
    // 确保调用回调函数
    if (onCategoryChange) {
      onCategoryChange(categoryId);
    }
    setHoveredCategory(null);
    setIsHovering(false);
    setActiveSubCategory(null);
    
    // 添加调试信息
    console.log(`切换到分类: ${categoryId}`);
  };

  const handleMouseEnter = (categoryId: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setHoveredCategory(categoryId);
    setIsHovering(true);
  };

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setHoveredCategory(null);
      setIsHovering(false);
    }, 150);
  };

  const handleSubCategoryClick = (subCategory: string) => {
    if (hoveredCategory && onSubCategoryClick) {
      onSubCategoryClick(hoveredCategory, subCategory);
    }
    setActiveSubCategory(subCategory);
    setHoveredCategory(null);
    setIsHovering(false);
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <div className="bg-white/80 backdrop-blur-xl border-b border-slate-200/40 relative z-[60]">
      <div className="max-w-7xl mx-auto px-4">
        {/* Apple风格水平导航栏 */}
        <div className="flex items-center justify-center space-x-8 py-3">
          {mainCategories.map((category) => (
            <div
              key={category.id}
              className="relative"
              onMouseEnter={() => handleMouseEnter(category.id)}
              onMouseLeave={handleMouseLeave}
            >
              <Button
                variant="ghost"
                onClick={() => handleCategoryClick(category.id)}
                className={`px-3 py-2 text-sm transition-colors duration-200 ${
                  activeCategory === category.id
                    ? 'text-slate-900'
                    : 'text-slate-600 hover:text-slate-900'
                }`}
              >
                {category.label}
              </Button>
            </div>
          ))}
        </div>

        {/* 下拉菜单 */}
        {isHovering && hoveredCategory && subCategories[hoveredCategory as keyof typeof subCategories] && (
          <div
            ref={dropdownRef}
            className="absolute left-0 right-0 top-full bg-white/95 backdrop-blur-xl border-b border-slate-200/40 shadow-lg"
            onMouseEnter={() => {
              if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
              }
              setIsHovering(true);
            }}
            onMouseLeave={handleMouseLeave}
          >
            <div className="max-w-7xl mx-auto px-4 py-6">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
                {subCategories[hoveredCategory as keyof typeof subCategories]?.sections.map((section, sectionIndex) => (
                  <div key={sectionIndex} className="space-y-3">
                    <h3 className="font-semibold text-slate-900 text-sm">{section.title}</h3>
                    <ul className="space-y-2">
                      {section.items.map((item, itemIndex) => (
                        <li key={itemIndex}>
                          <button
                            onClick={() => handleSubCategoryClick(item)}
                            className={`text-sm transition-colors duration-200 hover:text-blue-600 ${
                              activeSubCategory === item
                                ? 'text-blue-600 font-medium'
                                : 'text-slate-600'
                            }`}
                          >
                            {item}
                          </button>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
