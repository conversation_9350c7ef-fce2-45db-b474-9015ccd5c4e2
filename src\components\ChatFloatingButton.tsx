import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MessageCircle } from "lucide-react";

interface ChatFloatingButtonProps {
  onClick: () => void;
  notificationCount?: number;
  isVisible?: boolean;
}

export function ChatFloatingButton({ 
  onClick, 
  notificationCount = 0, 
  isVisible = true 
}: ChatFloatingButtonProps) {
  if (!isVisible) return null;

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <Button
        onClick={onClick}
        size="lg"
        className="relative h-14 w-14 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-300 group"
      >
        <MessageCircle className="h-6 w-6 text-white group-hover:scale-110 transition-transform" />
        
        {/* 通知数量徽章 */}
        {notificationCount > 0 && (
          <Badge className="absolute -top-2 -right-2 h-6 w-6 p-0 flex items-center justify-center text-xs bg-red-500 text-white border-2 border-white">
            {notificationCount > 99 ? '99+' : notificationCount}
          </Badge>
        )}
        
        {/* 脉冲动画 */}
        <div className="absolute inset-0 rounded-full bg-blue-600 animate-ping opacity-20"></div>
      </Button>
    </div>
  );
}
