import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { registerSchema, validateAndSanitize } from '@/lib/validations/auth';
import { UserRepository } from '@/lib/database/users';
import { API_ENDPOINTS } from '@/lib/api-endpoints';

// JWT密钥配置
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

// 统一响应格式
function createResponse(success: boolean, data?: any, message?: string, code?: string, status: number = 200) {
  return NextResponse.json({
    success,
    data,
    message,
    code,
  }, { status });
}

// 错误响应
function createErrorResponse(message: string, code?: string, status: number = 400, errors?: Record<string, string[]>) {
  return NextResponse.json({
    success: false,
    message,
    code,
    errors,
  }, { status });
}

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    const validation = validateAndSanitize(registerSchema, body);
    if (!validation.success) {
      return createErrorResponse(
        '输入信息有误，请检查后重试',
        'VALIDATION_ERROR',
        422,
        validation.errors
      );
    }

    const { username, password, email } = validation.data;

    // 创建用户
    const user = await UserRepository.create({
      username,
      password,
      email,
    });

    // 生成JWT token
    const token = jwt.sign(
      {
        userId: user.id,
        username: user.username,
        type: 'access'
      },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN }
    );

    // 生成refresh token
    const refreshToken = jwt.sign(
      {
        userId: user.id,
        username: user.username,
        type: 'refresh'
      },
      JWT_SECRET,
      { expiresIn: '30d' }
    );

    // 返回成功响应
    return createResponse(true, {
      user: {
        id: user.id.toString(),
        username: user.username,
        email: user.email,
        createdAt: user.created_at,
      },
      token,
      refreshToken,
      expiresIn: 7 * 24 * 60 * 60, // 7天，单位：秒
    }, '注册成功', undefined, 201);

  } catch (error) {
    console.error('Register error:', error);

    // 处理用户已存在错误
    if (error instanceof Error) {
      if (error.message.includes('用户名已存在')) {
        return createErrorResponse(
          '用户名已存在，请选择其他用户名',
          'DUPLICATE_USERNAME',
          409
        );
      }

      if (error.message.includes('邮箱已被使用')) {
        return createErrorResponse(
          '邮箱已被使用，请选择其他邮箱',
          'DUPLICATE_EMAIL',
          409
        );
      }

      if (error.message.includes('数据库连接失败') || error.message.includes('ECONNREFUSED')) {
        return createErrorResponse(
          '数据库连接失败，请稍后重试',
          'DATABASE_CONNECTION_ERROR',
          503
        );
      }
    }

    // 其他错误
    return createErrorResponse(
      '注册失败，请稍后重试',
      'INTERNAL_SERVER_ERROR',
      500
    );
  }
}

// 处理不支持的HTTP方法
export async function GET() {
  return createErrorResponse(
    '不支持的请求方法',
    'METHOD_NOT_ALLOWED',
    405
  );
}

export async function PUT() {
  return createErrorResponse(
    '不支持的请求方法',
    'METHOD_NOT_ALLOWED',
    405
  );
}

export async function DELETE() {
  return createErrorResponse(
    '不支持的请求方法',
    'METHOD_NOT_ALLOWED',
    405
  );
}
