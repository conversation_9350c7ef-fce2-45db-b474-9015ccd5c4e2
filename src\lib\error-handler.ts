import toast from 'react-hot-toast';
import { ApiClientError } from './api-client';

// 错误类型定义
export interface ErrorInfo {
  message: string;
  code?: string;
  status?: number;
  errors?: Record<string, string[]>;
}

// 错误处理器类
export class ErrorHandler {
  // 显示错误提示
  static showError(error: ApiClientError | Error | string) {
    let errorInfo: ErrorInfo;

    if (typeof error === 'string') {
      errorInfo = { message: error };
    } else if (error instanceof ApiClientError) {
      errorInfo = {
        message: error.message,
        code: error.code,
        status: error.status,
        errors: error.errors,
      };
    } else {
      errorInfo = { message: error.message || 'Unknown error' };
    }

    // 根据错误类型显示不同的提示
    this.displayErrorToast(errorInfo);
    
    // 记录错误日志
    this.logError(errorInfo);
  }

  // 显示成功提示
  static showSuccess(message: string) {
    toast.success(message, {
      duration: 3000,
      position: 'top-right',
    });
  }

  // 显示警告提示
  static showWarning(message: string) {
    toast(message, {
      duration: 4000,
      position: 'top-right',
      icon: '⚠️',
    });
  }

  // 显示信息提示
  static showInfo(message: string) {
    toast(message, {
      duration: 3000,
      position: 'top-right',
      icon: 'ℹ️',
    });
  }

  // 显示加载提示
  static showLoading(message: string = 'Loading...') {
    return toast.loading(message, {
      position: 'top-right',
    });
  }

  // 关闭加载提示
  static dismissLoading(toastId: string) {
    toast.dismiss(toastId);
  }

  // 显示错误Toast
  private static displayErrorToast(errorInfo: ErrorInfo) {
    let message = errorInfo.message;

    // 如果有字段验证错误，显示第一个错误
    if (errorInfo.errors) {
      const firstError = Object.values(errorInfo.errors)[0];
      if (firstError && firstError.length > 0) {
        message = firstError[0];
      }
    }

    // 根据错误码或状态码自定义消息
    const customMessage = this.getCustomErrorMessage(errorInfo);
    if (customMessage) {
      message = customMessage;
    }

    toast.error(message, {
      duration: 5000,
      position: 'top-right',
    });
  }

  // 获取自定义错误消息
  private static getCustomErrorMessage(errorInfo: ErrorInfo): string | null {
    // 根据HTTP状态码
    switch (errorInfo.status) {
      case 400:
        return '请求参数错误，请检查输入信息';
      case 401:
        return '登录已过期，请重新登录';
      case 403:
        return '没有权限执行此操作';
      case 404:
        return '请求的资源不存在';
      case 409:
        return '数据冲突，请刷新页面后重试';
      case 422:
        return '数据验证失败，请检查输入信息';
      case 429:
        return '请求过于频繁，请稍后再试';
      case 500:
        return '服务器内部错误，请稍后重试';
      case 502:
        return '服务暂时不可用，请稍后重试';
      case 503:
        return '服务维护中，请稍后重试';
      case 504:
        return '请求超时，请检查网络连接';
    }

    // 根据错误码
    switch (errorInfo.code) {
      case 'TIMEOUT':
        return '请求超时，请检查网络连接';
      case 'NETWORK_ERROR':
        return '网络连接失败，请检查网络设置';
      case 'VALIDATION_ERROR':
        return '输入信息有误，请检查后重试';
      case 'AUTH_FAILED':
        return '认证失败，请重新登录';
      case 'PERMISSION_DENIED':
        return '权限不足，无法执行此操作';
      case 'RESOURCE_NOT_FOUND':
        return '请求的资源不存在';
      case 'DUPLICATE_RESOURCE':
        return '资源已存在，请勿重复操作';
      case 'RATE_LIMIT_EXCEEDED':
        return '操作过于频繁，请稍后再试';
      default:
        return null;
    }
  }

  // 记录错误日志
  private static logError(errorInfo: ErrorInfo) {
    // 在开发环境下打印详细错误信息
    if (process.env.NODE_ENV === 'development') {
      console.error('API Error:', errorInfo);
    }

    // 在生产环境下可以发送到错误监控服务
    if (process.env.NODE_ENV === 'production') {
      // 这里可以集成Sentry、LogRocket等错误监控服务
      // 例如：Sentry.captureException(errorInfo);
    }
  }

  // 处理表单验证错误
  static handleValidationErrors(errors: Record<string, string[]>): Record<string, string> {
    const formErrors: Record<string, string> = {};
    
    Object.entries(errors).forEach(([field, messages]) => {
      if (messages && messages.length > 0) {
        formErrors[field] = messages[0];
      }
    });

    return formErrors;
  }

  // 检查是否为认证错误
  static isAuthError(error: ApiClientError): boolean {
    return error.status === 401 || error.code === 'AUTH_FAILED';
  }

  // 检查是否为权限错误
  static isPermissionError(error: ApiClientError): boolean {
    return error.status === 403 || error.code === 'PERMISSION_DENIED';
  }

  // 检查是否为网络错误
  static isNetworkError(error: ApiClientError): boolean {
    return error.code === 'NETWORK_ERROR' || error.code === 'TIMEOUT';
  }

  // 检查是否为验证错误
  static isValidationError(error: ApiClientError): boolean {
    return error.status === 422 || error.code === 'VALIDATION_ERROR' || !!error.errors;
  }
}

// 全局错误处理函数
export const handleApiError = (error: ApiClientError | Error | string) => {
  ErrorHandler.showError(error);
};

// 成功提示函数
export const showSuccess = (message: string) => {
  ErrorHandler.showSuccess(message);
};

// 警告提示函数
export const showWarning = (message: string) => {
  ErrorHandler.showWarning(message);
};

// 信息提示函数
export const showInfo = (message: string) => {
  ErrorHandler.showInfo(message);
};

// 加载提示函数
export const showLoading = (message?: string) => {
  return ErrorHandler.showLoading(message);
};

// 关闭加载提示函数
export const dismissLoading = (toastId: string) => {
  ErrorHandler.dismissLoading(toastId);
};

export default ErrorHandler;
