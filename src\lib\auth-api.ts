import apiClient, { ApiResponse } from './api-client';

// 认证相关的类型定义
export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  password: string;
  confirmPassword?: string;
}

export interface AuthResponse {
  user: {
    id: string;
    username: string;
    email?: string;
    avatar?: string;
    createdAt: string;
  };
  token: string;
  refreshToken?: string;
  expiresIn: number;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ResetPasswordRequest {
  email: string;
}

export interface ConfirmResetPasswordRequest {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

// 认证API类
export class AuthAPI {
  // 用户注册
  static async register(data: RegisterRequest): Promise<ApiResponse<AuthResponse>> {
    return apiClient.post<AuthResponse>('/auth/register', data);
  }

  // 用户登录
  static async login(data: LoginRequest): Promise<ApiResponse<AuthResponse>> {
    return apiClient.post<AuthResponse>('/auth/login', data);
  }

  // 用户登出
  static async logout(): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/auth/logout');
  }

  // 刷新Token
  static async refreshToken(data: RefreshTokenRequest): Promise<ApiResponse<AuthResponse>> {
    return apiClient.post<AuthResponse>('/auth/refresh', data);
  }

  // 获取当前用户信息
  static async getCurrentUser(): Promise<ApiResponse<AuthResponse['user']>> {
    return apiClient.get<AuthResponse['user']>('/auth/me');
  }

  // 修改密码
  static async changePassword(data: ChangePasswordRequest): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/auth/change-password', data);
  }

  // 请求重置密码
  static async requestPasswordReset(data: ResetPasswordRequest): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/auth/reset-password', data);
  }

  // 确认重置密码
  static async confirmPasswordReset(data: ConfirmResetPasswordRequest): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/auth/reset-password/confirm', data);
  }

  // 验证Token是否有效
  static async validateToken(): Promise<ApiResponse<{ valid: boolean }>> {
    return apiClient.get<{ valid: boolean }>('/auth/validate');
  }
}

// 用户相关API
export class UserAPI {
  // 更新用户资料
  static async updateProfile(data: {
    username?: string;
    email?: string;
    avatar?: string;
    bio?: string;
  }): Promise<ApiResponse<AuthResponse['user']>> {
    return apiClient.patch<AuthResponse['user']>('/user/profile', data);
  }

  // 上传头像
  static async uploadAvatar(file: File): Promise<ApiResponse<{ avatar: string }>> {
    const formData = new FormData();
    formData.append('avatar', file);
    return apiClient.upload<{ avatar: string }>('/user/avatar', formData);
  }

  // 删除账户
  static async deleteAccount(password: string): Promise<ApiResponse<void>> {
    return apiClient.post<void>('/user/delete', { password });
  }

  // 获取用户统计信息
  static async getUserStats(): Promise<ApiResponse<{
    ordersCount: number;
    favoritesCount: number;
    reviewsCount: number;
    totalSpent: number;
  }>> {
    return apiClient.get('/user/stats');
  }
}

// 导出所有API
export const authAPI = AuthAPI;
export const userAPI = UserAPI;

// 默认导出
export default {
  auth: AuthAPI,
  user: UserAPI,
};
