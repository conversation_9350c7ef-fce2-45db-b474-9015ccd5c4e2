import { useState, useMemo } from "react";
import { Heart, Search, Filter, Star, MapPin, Calendar } from "lucide-react";
import { <PERSON><PERSON> } from "./ui/button";
import { Input } from "./ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Badge } from "./ui/badge";
import { Card, CardContent } from "./ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";

import { mockFavorites } from "../data/mockFavorites";
import { FavoriteItem } from "../types/favorites";

export function FavoritesPageContent() {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("latest");
  const [filterCategory, setFilterCategory] = useState("all");

  // 筛选和排序收藏商品
  const filteredAndSortedFavorites = useMemo(() => {
    let filtered = mockFavorites;

    // 搜索筛选
    if (searchTerm) {
      const lowerSearchTerm = searchTerm.toLowerCase();
      filtered = filtered.filter((item) =>
        item.title.toLowerCase().includes(lowerSearchTerm) ||
        item.description.toLowerCase().includes(lowerSearchTerm) ||
        item.creator.name.toLowerCase().includes(lowerSearchTerm)
      );
    }

    // 分类筛选
    if (filterCategory !== "all") {
      filtered = filtered.filter(item => item.category === filterCategory);
    }

    // 排序
    const sorted = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case "latest":
          return new Date(b.addedAt).getTime() - new Date(a.addedAt).getTime();
        case "price-low":
          return a.price - b.price;
        case "price-high":
          return b.price - a.price;
        case "rating":
          return b.rating - a.rating;
        default:
          return 0;
      }
    });

    return sorted;
  }, [searchTerm, sortBy, filterCategory]);

  // 获取所有分类
  const categories = [...new Set(mockFavorites.map(item => item.category))];

  const handleRemoveFavorite = (id: string) => {
    console.log(`移除收藏: ${id}`);
    // 这里可以添加实际的移除收藏逻辑
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
  };

  const renderServiceCard = (service: FavoriteItem) => (
    <Card key={service.id} className="group overflow-hidden border-0 bg-white/70 backdrop-blur-sm hover:bg-white hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
      <CardContent className="p-0">
        {/* 商品图片 */}
        <div className="relative aspect-[4/3] overflow-hidden">
          <img
            src={service.image}
            alt={service.title}
            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
          />
          
          {/* 收藏移除按钮 */}
          <button
            onClick={() => handleRemoveFavorite(service.id)}
            className="absolute top-3 right-3 w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center text-red-500 hover:bg-white hover:scale-110 transition-all duration-200 shadow-lg"
          >
            <Heart className="h-4 w-4 fill-current" />
          </button>

          {/* 商品标签 */}
          {service.tags && service.tags.length > 0 && (
            <div className="absolute bottom-3 left-3 flex gap-1.5">
              {service.tags.map((tag, index) => {
                const tagColors = {
                  "U pro": "bg-gradient-to-r from-blue-500 to-indigo-500 text-white",
                  "好评Top": "bg-gradient-to-r from-green-500 to-emerald-500 text-white", 
                  "技能之星": "bg-gradient-to-r from-purple-500 to-pink-500 text-white"
                };
                return (
                  <Badge 
                    key={index}
                    className={`text-xs px-2 py-0.5 border-0 ${tagColors[tag as keyof typeof tagColors] || 'bg-gray-500 text-white'}`}
                  >
                    {tag}
                  </Badge>
                );
              })}
            </div>
          )}
        </div>

        {/* 商品信息 */}
        <div className="p-4">
          {/* 创作者信息 */}
          <div className="flex items-center gap-2 mb-3">
            <Avatar className="h-6 w-6">
              <AvatarImage src={service.creator.avatar} />
              <AvatarFallback className="text-xs">{service.creator.name[0]}</AvatarFallback>
            </Avatar>
            <span className="text-sm text-slate-600 truncate">{service.creator.name}</span>
            {service.creator.isOnline && (
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            )}
            <div className="flex items-center gap-1 text-xs text-slate-500 ml-auto">
              <MapPin className="h-3 w-3" />
              {service.creator.location}
            </div>
          </div>

          {/* 商品标题 */}
          <h3 className="font-medium text-slate-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
            {service.title}
          </h3>

          {/* 评分和评价数 */}
          <div className="flex items-center gap-2 mb-3">
            <div className="flex items-center gap-1">
              <Star className="h-4 w-4 text-yellow-500 fill-current" />
              <span className="text-sm font-medium text-slate-700">{service.rating}</span>
            </div>
            <span className="text-sm text-slate-500">({service.reviewCount} 评价)</span>
          </div>

          {/* 价格和收藏时间 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-lg font-semibold text-slate-900">¥{service.price}</span>
              {service.originalPrice && (
                <span className="text-sm text-slate-500 line-through">¥{service.originalPrice}</span>
              )}
            </div>
            <div className="flex items-center gap-1 text-xs text-slate-500">
              <Calendar className="h-3 w-3" />
              {formatDate(service.addedAt)}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      {/* 页面头部 */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <div className="relative">
            <div className="absolute inset-0 bg-red-500/20 rounded-xl blur-sm"></div>
            <div className="relative p-3 bg-gradient-to-br from-red-500/10 to-pink-500/10 rounded-xl border border-red-200/30">
              <Heart className="h-6 w-6 text-red-600 fill-current" />
            </div>
          </div>
          <div>
            <h1 className="text-2xl font-semibold text-slate-900">我的收藏</h1>
            <p className="text-slate-600">已收藏 {mockFavorites.length} 个专业服务</p>
          </div>
        </div>

        {/* 搜索和筛选栏 */}
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between bg-white/60 backdrop-blur-sm rounded-2xl p-4 border border-slate-200/60">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                placeholder="搜索收藏的服务..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-white/80 border-slate-200/60 focus:bg-white"
              />
            </div>
          </div>
          
          <div className="flex gap-3">
            <Select value={filterCategory} onValueChange={setFilterCategory}>
              <SelectTrigger className="w-32 bg-white/80 border-slate-200/60">
                <SelectValue placeholder="分类" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部分类</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-32 bg-white/80 border-slate-200/60">
                <SelectValue placeholder="排序" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="latest">最新收藏</SelectItem>
                <SelectItem value="price-low">价格从低到高</SelectItem>
                <SelectItem value="price-high">价格从高到低</SelectItem>
                <SelectItem value="rating">评分最高</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* 收藏列表 */}
      {filteredAndSortedFavorites.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredAndSortedFavorites.map(renderServiceCard)}
        </div>
      ) : (
        <div className="text-center py-16">
          <div className="relative inline-block mb-4">
            <div className="absolute inset-0 bg-slate-500/10 rounded-xl blur-sm"></div>
            <div className="relative p-6 bg-gradient-to-br from-slate-500/10 to-slate-500/5 rounded-xl border border-slate-200/30">
              <Heart className="h-12 w-12 text-slate-400 mx-auto" />
            </div>
          </div>
          <h3 className="text-lg font-medium text-slate-900 mb-2">
            {searchTerm || filterCategory !== "all" ? "没有找到相关收藏" : "暂无收藏内容"}
          </h3>
          <p className="text-slate-600 mb-6">
            {searchTerm || filterCategory !== "all" 
              ? "尝试调整搜索条件或筛选选项" 
              : "还没有收藏任何服务，快去发现优质服务吧！"
            }
          </p>
          {!searchTerm && filterCategory === "all" && (
            <Button 
              onClick={() => window.location.href = "/services"}
              className="bg-blue-600 hover:bg-blue-700"
            >
              去逛逛
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
