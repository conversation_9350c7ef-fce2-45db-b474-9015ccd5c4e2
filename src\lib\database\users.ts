import { query, transaction, handleDatabaseError } from './connection';
import bcrypt from 'bcryptjs';
import { RowDataPacket, ResultSetHeader } from 'mysql2';

// 用户数据类型
export interface User {
  id: number;
  username: string;
  email?: string;
  avatar?: string;
  bio?: string;
  status: 'active' | 'inactive' | 'banned';
  email_verified_at?: Date;
  last_login_at?: Date;
  created_at: Date;
  updated_at: Date;
}

// 用户创建数据
export interface CreateUserData {
  username: string;
  password: string;
  email?: string;
}

// 用户更新数据
export interface UpdateUserData {
  username?: string;
  email?: string;
  avatar?: string;
  bio?: string;
  status?: 'active' | 'inactive' | 'banned';
}

// 用户数据库操作类
export class UserRepository {
  // 根据用户名查找用户
  static async findByUsername(username: string): Promise<User | null> {
    try {
      const users = await query<(User & RowDataPacket)[]>(
        'SELECT * FROM users WHERE username = ? AND status != "deleted"',
        [username]
      );
      
      return users.length > 0 ? users[0] : null;
    } catch (error) {
      throw handleDatabaseError(error);
    }
  }

  // 根据邮箱查找用户
  static async findByEmail(email: string): Promise<User | null> {
    try {
      const users = await query<(User & RowDataPacket)[]>(
        'SELECT * FROM users WHERE email = ? AND status != "deleted"',
        [email]
      );
      
      return users.length > 0 ? users[0] : null;
    } catch (error) {
      throw handleDatabaseError(error);
    }
  }

  // 根据ID查找用户
  static async findById(id: number): Promise<User | null> {
    try {
      const users = await query<(User & RowDataPacket)[]>(
        'SELECT * FROM users WHERE id = ? AND status != "deleted"',
        [id]
      );
      
      return users.length > 0 ? users[0] : null;
    } catch (error) {
      throw handleDatabaseError(error);
    }
  }

  // 创建用户
  static async create(userData: CreateUserData): Promise<User> {
    try {
      return await transaction(async (connection) => {
        // 检查用户名是否已存在
        const [existingUsers] = await connection.execute(
          'SELECT id FROM users WHERE username = ?',
          [userData.username]
        );

        if (Array.isArray(existingUsers) && existingUsers.length > 0) {
          throw new Error('用户名已存在');
        }

        // 检查邮箱是否已存在（如果提供了邮箱）
        if (userData.email) {
          const [existingEmails] = await connection.execute(
            'SELECT id FROM users WHERE email = ?',
            [userData.email]
          );

          if (Array.isArray(existingEmails) && existingEmails.length > 0) {
            throw new Error('邮箱已被使用');
          }
        }

        // 加密密码
        const saltRounds = 12;
        const hashedPassword = await bcrypt.hash(userData.password, saltRounds);

        // 插入用户数据
        const [result] = await connection.execute(
          `INSERT INTO users (username, password, email, created_at, updated_at) 
           VALUES (?, ?, ?, NOW(), NOW())`,
          [userData.username, hashedPassword, userData.email || null]
        ) as [ResultSetHeader, any];

        // 获取创建的用户
        const [userRows] = await connection.execute(
          'SELECT * FROM users WHERE id = ?',
          [result.insertId]
        );

        const user = Array.isArray(userRows) ? userRows[0] as User : null;
        if (!user) {
          throw new Error('用户创建失败');
        }

        return user;
      });
    } catch (error) {
      throw handleDatabaseError(error);
    }
  }

  // 验证用户密码
  static async verifyPassword(username: string, password: string): Promise<User | null> {
    try {
      const users = await query<(User & { password: string } & RowDataPacket)[]>(
        'SELECT * FROM users WHERE username = ? AND status = "active"',
        [username]
      );

      if (users.length === 0) {
        return null;
      }

      const user = users[0];
      const isPasswordValid = await bcrypt.compare(password, user.password);

      if (!isPasswordValid) {
        return null;
      }

      // 移除密码字段
      const { password: _, ...userWithoutPassword } = user;
      return userWithoutPassword as User;
    } catch (error) {
      throw handleDatabaseError(error);
    }
  }

  // 更新用户信息
  static async update(id: number, userData: UpdateUserData): Promise<User | null> {
    try {
      return await transaction(async (connection) => {
        // 构建更新字段
        const updateFields: string[] = [];
        const updateValues: any[] = [];

        if (userData.username !== undefined) {
          // 检查用户名是否已被其他用户使用
          const [existingUsers] = await connection.execute(
            'SELECT id FROM users WHERE username = ? AND id != ?',
            [userData.username, id]
          );

          if (Array.isArray(existingUsers) && existingUsers.length > 0) {
            throw new Error('用户名已被使用');
          }

          updateFields.push('username = ?');
          updateValues.push(userData.username);
        }

        if (userData.email !== undefined) {
          // 检查邮箱是否已被其他用户使用
          if (userData.email) {
            const [existingEmails] = await connection.execute(
              'SELECT id FROM users WHERE email = ? AND id != ?',
              [userData.email, id]
            );

            if (Array.isArray(existingEmails) && existingEmails.length > 0) {
              throw new Error('邮箱已被使用');
            }
          }

          updateFields.push('email = ?');
          updateValues.push(userData.email);
        }

        if (userData.avatar !== undefined) {
          updateFields.push('avatar = ?');
          updateValues.push(userData.avatar);
        }

        if (userData.bio !== undefined) {
          updateFields.push('bio = ?');
          updateValues.push(userData.bio);
        }

        if (userData.status !== undefined) {
          updateFields.push('status = ?');
          updateValues.push(userData.status);
        }

        if (updateFields.length === 0) {
          // 没有需要更新的字段，直接返回当前用户
          return await this.findById(id);
        }

        // 添加更新时间
        updateFields.push('updated_at = NOW()');
        updateValues.push(id);

        // 执行更新
        await connection.execute(
          `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`,
          updateValues
        );

        // 返回更新后的用户
        const [userRows] = await connection.execute(
          'SELECT * FROM users WHERE id = ?',
          [id]
        );

        return Array.isArray(userRows) && userRows.length > 0 ? userRows[0] as User : null;
      });
    } catch (error) {
      throw handleDatabaseError(error);
    }
  }

  // 更新最后登录时间
  static async updateLastLogin(id: number): Promise<void> {
    try {
      await query(
        'UPDATE users SET last_login_at = NOW(), updated_at = NOW() WHERE id = ?',
        [id]
      );
    } catch (error) {
      throw handleDatabaseError(error);
    }
  }

  // 修改密码
  static async changePassword(id: number, newPassword: string): Promise<void> {
    try {
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

      await query(
        'UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?',
        [hashedPassword, id]
      );
    } catch (error) {
      throw handleDatabaseError(error);
    }
  }

  // 删除用户（软删除）
  static async delete(id: number): Promise<void> {
    try {
      await query(
        'UPDATE users SET status = "deleted", updated_at = NOW() WHERE id = ?',
        [id]
      );
    } catch (error) {
      throw handleDatabaseError(error);
    }
  }

  // 获取用户统计信息
  static async getStats(userId: number): Promise<{
    ordersCount: number;
    favoritesCount: number;
    reviewsCount: number;
    servicesCount: number;
  }> {
    try {
      const [stats] = await query<any[]>(`
        SELECT 
          (SELECT COUNT(*) FROM orders WHERE buyer_id = ?) as ordersCount,
          (SELECT COUNT(*) FROM favorites WHERE user_id = ?) as favoritesCount,
          (SELECT COUNT(*) FROM reviews WHERE reviewer_id = ?) as reviewsCount,
          (SELECT COUNT(*) FROM services WHERE user_id = ? AND status != 'deleted') as servicesCount
      `, [userId, userId, userId, userId]);

      return {
        ordersCount: stats.ordersCount || 0,
        favoritesCount: stats.favoritesCount || 0,
        reviewsCount: stats.reviewsCount || 0,
        servicesCount: stats.servicesCount || 0,
      };
    } catch (error) {
      throw handleDatabaseError(error);
    }
  }
}

export default UserRepository;
