"use client";

import { useState } from "react";
import { NavigationV78 } from "@/components/NavigationV78";
import { CategoryNavigation } from "@/components/CategoryNavigation";
import { PlaceholderPage } from "@/components/PlaceholderPage";
import { ChatWindow } from "@/components/ChatWindow";
import { ChatFloatingButton } from "@/components/ChatFloatingButton";
import { useRouter } from "next/navigation";

export default function Support() {
  const router = useRouter();
  const [activeCategory, setActiveCategory] = useState("popular");
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [notificationCount] = useState(3);

  const handleNavigate = (page: string) => {
    switch (page) {
      case "home":
      case "landing":
        router.push("/");
        break;
      case "services":
        router.push("/services");
        break;
      case "orders":
        router.push("/orders");
        break;
      case "favorites":
        router.push("/favorites");
        break;
      case "support":
        router.push("/support");
        break;
      case "gallery":
        router.push("/gallery");
        break;
      case "login":
        router.push("/login");
        break;
      case "register":
        router.push("/register");
        break;
      default:
        break;
    }
  };

  const handleSubCategoryClick = (category: string, subCategory: string) => {
    setActiveCategory(category);
    console.log(`已选择子分类: ${subCategory} 在分类: ${category}`);
  };

  const handleCategoryChange = (category: string) => {
    setActiveCategory(category);
    console.log(`已切换到分类: ${category}`);
  };

  const handleOpenChat = () => {
    setIsChatOpen(true);
  };

  const handleCloseChat = () => {
    setIsChatOpen(false);
  };

  const handleMinimizeChat = () => {
    setIsChatOpen(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50/30">
      <NavigationV78 
        currentPage="support" 
        onNavigate={handleNavigate}
        onOpenChat={handleOpenChat}
      />
      
      <CategoryNavigation 
        activeCategory={activeCategory}
        onCategoryChange={handleCategoryChange}
        onSubCategoryClick={handleSubCategoryClick}
      />
      
      <PlaceholderPage
        title="定制服务"
        description="专业定制服务平台正在建设中，敬请期待..."
        iconColor="text-green-600"
        gradientColors="from-green-500/10 to-emerald-500/10"
        borderColor="border-green-200/30"
      />

      {/* 聊天窗口 */}
      <ChatWindow isOpen={isChatOpen} onClose={handleCloseChat} onMinimize={handleMinimizeChat} />
      
      {/* 聊天悬浮球 */}
      <ChatFloatingButton 
        onClick={handleOpenChat}
        notificationCount={notificationCount}
        isVisible={!isChatOpen}
      />
    </div>
  );
}
