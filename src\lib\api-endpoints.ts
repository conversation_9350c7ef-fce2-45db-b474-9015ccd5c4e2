// API接口端点统一管理
// 所有接口地址都在这里定义，方便维护和查看

export const API_ENDPOINTS = {
  // 认证相关接口
  AUTH: {
    LOGIN: '/auth/login',                    // POST - 用户登录
    REGISTER: '/auth/register',              // POST - 用户注册
    LOGOUT: '/auth/logout',                  // POST - 用户登出
    REFRESH: '/auth/refresh',                // POST - 刷新Token
    VALIDATE: '/auth/validate',              // GET - 验证Token
    ME: '/auth/me',                          // GET - 获取当前用户信息
    CHANGE_PASSWORD: '/auth/change-password', // POST - 修改密码
    RESET_PASSWORD: '/auth/reset-password',   // POST - 请求重置密码
    RESET_PASSWORD_CONFIRM: '/auth/reset-password/confirm', // POST - 确认重置密码
  },

  // 用户相关接口
  USER: {
    PROFILE: '/user/profile',                // GET/PATCH - 获取/更新用户资料
    AVATAR: '/user/avatar',                  // POST - 上传头像
    DELETE: '/user/delete',                  // POST - 删除账户
    STATS: '/user/stats',                    // GET - 获取用户统计信息
  },

  // 首页相关接口
  HOME: {
    BANNER: '/home/<USER>',                  // GET - 获取首页轮播图
    CATEGORIES: '/home/<USER>',          // GET - 获取热门分类
    FEATURED_SERVICES: '/home/<USER>',     // GET - 获取精选服务
    STATS: '/home/<USER>',                    // GET - 获取网站统计数据
  },

  // 服务相关接口
  SERVICES: {
    LIST: '/services',                       // GET - 获取服务列表
    DETAIL: '/services/:id',                 // GET - 获取服务详情
    CREATE: '/services',                     // POST - 创建服务
    UPDATE: '/services/:id',                 // PUT/PATCH - 更新服务
    DELETE: '/services/:id',                 // DELETE - 删除服务
    SEARCH: '/services/search',              // GET - 搜索服务
    CATEGORIES: '/services/categories',      // GET - 获取服务分类
    REVIEWS: '/services/:id/reviews',        // GET - 获取服务评价
  },

  // 订单相关接口
  ORDERS: {
    LIST: '/orders',                         // GET - 获取订单列表
    DETAIL: '/orders/:id',                   // GET - 获取订单详情
    CREATE: '/orders',                       // POST - 创建订单
    UPDATE: '/orders/:id',                   // PATCH - 更新订单状态
    CANCEL: '/orders/:id/cancel',            // POST - 取消订单
    COMPLETE: '/orders/:id/complete',        // POST - 完成订单
    PAYMENT: '/orders/:id/payment',          // POST - 订单支付
  },

  // 收藏相关接口
  FAVORITES: {
    LIST: '/favorites',                      // GET - 获取收藏列表
    ADD: '/favorites',                       // POST - 添加收藏
    REMOVE: '/favorites/:id',                // DELETE - 移除收藏
    CHECK: '/favorites/check/:serviceId',    // GET - 检查是否已收藏
  },

  // 评价相关接口
  REVIEWS: {
    LIST: '/reviews',                        // GET - 获取评价列表
    CREATE: '/reviews',                      // POST - 创建评价
    UPDATE: '/reviews/:id',                  // PATCH - 更新评价
    DELETE: '/reviews/:id',                  // DELETE - 删除评价
  },

  // 文件上传接口
  UPLOAD: {
    IMAGE: '/upload/image',                  // POST - 上传图片
    FILE: '/upload/file',                    // POST - 上传文件
    AVATAR: '/upload/avatar',                // POST - 上传头像
  },

  // 搜索相关接口
  SEARCH: {
    SERVICES: '/search/services',            // GET - 搜索服务
    USERS: '/search/users',                  // GET - 搜索用户
    SUGGESTIONS: '/search/suggestions',      // GET - 搜索建议
  },

  // 消息通知接口
  NOTIFICATIONS: {
    LIST: '/notifications',                  // GET - 获取通知列表
    READ: '/notifications/:id/read',         // POST - 标记已读
    READ_ALL: '/notifications/read-all',     // POST - 全部标记已读
    DELETE: '/notifications/:id',            // DELETE - 删除通知
  },

  // 聊天相关接口
  CHAT: {
    CONVERSATIONS: '/chat/conversations',    // GET - 获取会话列表
    MESSAGES: '/chat/conversations/:id/messages', // GET - 获取消息列表
    SEND: '/chat/conversations/:id/send',    // POST - 发送消息
    CREATE_CONVERSATION: '/chat/conversations', // POST - 创建会话
  },

  // 管理员接口
  ADMIN: {
    USERS: '/admin/users',                   // GET - 获取用户列表
    SERVICES: '/admin/services',             // GET - 获取服务列表
    ORDERS: '/admin/orders',                 // GET - 获取订单列表
    STATS: '/admin/stats',                   // GET - 获取管理统计
    CATEGORIES: '/admin/categories',         // GET/POST/PUT/DELETE - 分类管理
  },
} as const;

// 接口方法类型定义
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

// 接口信息类型
export interface ApiEndpointInfo {
  path: string;
  method: HttpMethod;
  description: string;
  auth?: boolean; // 是否需要认证
  admin?: boolean; // 是否需要管理员权限
}

// 完整的接口信息映射
export const API_INFO: Record<string, Record<string, ApiEndpointInfo>> = {
  AUTH: {
    LOGIN: {
      path: API_ENDPOINTS.AUTH.LOGIN,
      method: 'POST',
      description: '用户登录',
      auth: false,
    },
    REGISTER: {
      path: API_ENDPOINTS.AUTH.REGISTER,
      method: 'POST',
      description: '用户注册',
      auth: false,
    },
    LOGOUT: {
      path: API_ENDPOINTS.AUTH.LOGOUT,
      method: 'POST',
      description: '用户登出',
      auth: true,
    },
    REFRESH: {
      path: API_ENDPOINTS.AUTH.REFRESH,
      method: 'POST',
      description: '刷新Token',
      auth: false,
    },
    VALIDATE: {
      path: API_ENDPOINTS.AUTH.VALIDATE,
      method: 'GET',
      description: '验证Token',
      auth: true,
    },
    ME: {
      path: API_ENDPOINTS.AUTH.ME,
      method: 'GET',
      description: '获取当前用户信息',
      auth: true,
    },
  },

  HOME: {
    BANNER: {
      path: API_ENDPOINTS.HOME.BANNER,
      method: 'GET',
      description: '获取首页轮播图',
      auth: false,
    },
    CATEGORIES: {
      path: API_ENDPOINTS.HOME.CATEGORIES,
      method: 'GET',
      description: '获取热门分类',
      auth: false,
    },
    FEATURED_SERVICES: {
      path: API_ENDPOINTS.HOME.FEATURED_SERVICES,
      method: 'GET',
      description: '获取精选服务',
      auth: false,
    },
    STATS: {
      path: API_ENDPOINTS.HOME.STATS,
      method: 'GET',
      description: '获取网站统计数据',
      auth: false,
    },
  },

  SERVICES: {
    LIST: {
      path: API_ENDPOINTS.SERVICES.LIST,
      method: 'GET',
      description: '获取服务列表',
      auth: false,
    },
    DETAIL: {
      path: API_ENDPOINTS.SERVICES.DETAIL,
      method: 'GET',
      description: '获取服务详情',
      auth: false,
    },
    CREATE: {
      path: API_ENDPOINTS.SERVICES.CREATE,
      method: 'POST',
      description: '创建服务',
      auth: true,
    },
    SEARCH: {
      path: API_ENDPOINTS.SERVICES.SEARCH,
      method: 'GET',
      description: '搜索服务',
      auth: false,
    },
  },

  FAVORITES: {
    LIST: {
      path: API_ENDPOINTS.FAVORITES.LIST,
      method: 'GET',
      description: '获取收藏列表',
      auth: true,
    },
    ADD: {
      path: API_ENDPOINTS.FAVORITES.ADD,
      method: 'POST',
      description: '添加收藏',
      auth: true,
    },
    REMOVE: {
      path: API_ENDPOINTS.FAVORITES.REMOVE,
      method: 'DELETE',
      description: '移除收藏',
      auth: true,
    },
  },
};

// 工具函数：替换路径参数
export function buildApiPath(path: string, params: Record<string, string | number>): string {
  let result = path;
  Object.entries(params).forEach(([key, value]) => {
    result = result.replace(`:${key}`, String(value));
  });
  return result;
}

// 工具函数：获取接口完整URL
export function getApiUrl(endpoint: string, params?: Record<string, string | number>): string {
  const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '/api';
  const path = params ? buildApiPath(endpoint, params) : endpoint;
  return `${baseUrl}${path}`;
}

// 导出默认对象
export default API_ENDPOINTS;
